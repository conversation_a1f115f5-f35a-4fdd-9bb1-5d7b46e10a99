//
//  LiveGiftBoardView.m
//  QCYZT
//
//  Created by shumi on 2023/5/10.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "LiveGiftBoardView.h"
#import "LiveGiftModel.h"
#import "HttpRequestTool+Live.h"
#import "HttpRequestTool+Pay.h"
#import "FMProgressHUD.h"
#import "FMPayTool.h"
#import "FMLiveDetailModel.h"
#import "FMPlayerManager.h"
#import "FMProgressHUD.h"

@interface GiftItemCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *giftIcon;
@property (nonatomic, strong) UILabel *nameLB;
@property (nonatomic, strong) UILabel *priceLB;
@property (nonatomic, strong) UILabel *choosedPriceLB;
@property (nonatomic, strong) LiveGiftModel *model;
@end

@implementation GiftItemCell

- (instancetype)initWithFrame:(CGRect)frame {
self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    self.layer.cornerRadius = 5;
    self.layer.borderWidth = 1;
    self.layer.masksToBounds = YES;
    
    UIImageView *giftIcon = [[UIImageView alloc] init];
    [self.contentView addSubview:giftIcon];
    [giftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(15);
        make.size.equalTo(CGSizeMake(50, 50));
    }];
    self.giftIcon = giftIcon;
    
    UILabel *nameLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.contentView addSubview:nameLB];
    [nameLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.left.right.equalTo(self).insets(UIEdgeInsetsMake(0, 5, 0, 5));
        make.top.equalTo(giftIcon.mas_bottom).offset(10);
        make.height.equalTo(21);
    }];
    self.nameLB = nameLB;
    
    UILabel *priceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.contentView addSubview:priceLB];
    [priceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(nameLB.mas_bottom).offset(2.5);
        make.height.equalTo(16.5);
    }];
    self.priceLB = priceLB;
    
    UILabel *choosedPriceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.whiteColor backgroundColor:UIColor.up_riseColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.contentView addSubview:choosedPriceLB];
    [choosedPriceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(30);
    }];
    self.choosedPriceLB = choosedPriceLB;
    self.choosedPriceLB.hidden = YES;
}

- (void)setModel:(LiveGiftModel *)model {
    _model = model;
    [self.giftIcon sd_setImageWithURL:[NSURL URLWithString:model.image] placeholderImage:nil];
    self.nameLB.text = model.name;
    self.priceLB.text = self.choosedPriceLB.text = [NSString stringWithFormat:@"%@金币",model.price];
    
    // 根据选中状态设置边框
    if (model.isSelected) {
        self.layer.borderColor = UIColor.up_riseColor.CGColor;
        self.contentView.backgroundColor = ColorWithHex(0xfff7f7);
        self.choosedPriceLB.hidden = NO;
    } else {
        self.layer.borderColor = UIColor.clearColor.CGColor;
        self.contentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        self.choosedPriceLB.hidden = YES;
    }
}


@end

@interface LiveGiftBoardView () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) NSMutableArray *itemViewArr;
@property (nonatomic, strong) UIView *backView;
/// 余额
@property (nonatomic, strong) UILabel *balanceLB;
/// 集合视图
@property (nonatomic, strong) UICollectionView *collectionView;
/// 打赏按钮
@property (nonatomic, strong) UIButton *sendGiftBtn;

@property (nonatomic,assign) CGFloat backViewHeight;
@property (nonatomic,assign) BOOL isLandScape;

// 积分相关属性
@property (nonatomic, assign) NSInteger userPoints;                        // 用户积分数量
@property (nonatomic, assign) NSInteger pointsRatio;                       // 积分兑换比例（多少积分=1金币）

// 积分UI组件
@property (nonatomic, strong) UILabel *pointsTitleLabel;                   // 积分标题
@property (nonatomic, strong) UILabel *pointsDescLabel;                    // 积分状态描述
@property (nonatomic, strong) UILabel *coinPayTitleLabel;                  // 金币支付标题
@property (nonatomic, strong) UILabel *coinPayAmountLabel;                 // 需支付金币数

@end

@implementation LiveGiftBoardView

- (instancetype)initPageLandScape:(BOOL)isLandScape {
    self = [super init];
    if (self) {
        self.isLandScape = isLandScape;
        // 增加高度以容纳积分UI和按钮
        self.backViewHeight = isLandScape ?  UI_SCREEN_HEIGHT : 450;
        
        // 初始化积分相关属性
        [self setupPointsData];
        
        [self setupUI:isLandScape];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(userCoinUpdate:) name:kUserCoinUpdate object:nil];
    }
    return self;
}

/// 初始化积分数据
- (void)setupPointsData {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.userPoints = userModel.points;
    self.pointsRatio = 1; // 1积分=1金币
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    if (self.isLandScape) {
        self.backView.frame = CGRectMake(UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT, 0, UI_SCREEN_HEIGHT, UI_SCREEN_HEIGHT);
    } else {
        self.backView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.backViewHeight, UI_SCREEN_WIDTH, self.backViewHeight);
    }
    // 每次显示时更新下金币余额和支付信息
    [self userCoinUpdate:nil];
}

/// 设置UI
- (void)setupUI:(BOOL)isLandScape {
    self.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    
    UIView *backView = [[UIView alloc] init];
    backView.backgroundColor = UIColor.up_contentBgColor;
    if (isLandScape) {
        self.backView.frame = CGRectMake(UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT, 0, UI_SCREEN_HEIGHT, UI_SCREEN_HEIGHT);
    } else {
        self.backView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.backViewHeight, UI_SCREEN_WIDTH, self.backViewHeight);
    }
    [self addSubview:backView];
   
    if (!isLandScape) {
        [backView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, self.backViewHeight) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    }
    self.backView = backView;
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.fm_market_nav_text_zeroColor backgroundColor:FMClearColor numberOfLines:1];
    titleLB.text = @"选择打赏金额";
    [backView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(@(13));
    }];
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:FMImgInBundle(@"导航/亮黑暗灰关闭") forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(titleLB);
        make.right.equalTo(@(-5));
        make.size.equalTo(@(CGSizeMake(20, 20)));
    }];

    UIView *line = [backView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.top.equalTo(@(50));
        make.left.right.equalTo(@(0));
        make.height.equalTo(@(0.5));
    }];
    line.backgroundColor = UIColor.fm_sepline_color;
    
    [backView addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line.mas_bottom).offset(20);
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.height.equalTo(130); // 固定高度，只显示一行
    }];
    
    // 设置积分和支付信息UI
    [self setupPaymentInfoView:backView];
    
    // 设置打赏按钮
    [self setupSendGiftView:backView];
}

/// 设置积分和支付信息UI
- (void)setupPaymentInfoView:(UIView *)backView {
    // 积分标题
    UILabel *pointsTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1];
    pointsTitleLabel.text = @"积分";
    [backView addSubview:pointsTitleLabel];
    [pointsTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(15));
        make.top.equalTo(self.collectionView.mas_bottom).offset(15);
    }];
    self.pointsTitleLabel = pointsTitleLabel;
    
    UIButton *helpButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [helpButton setImage:[UIImage imageNamed:@"pay_wenhao"] forState:UIControlStateNormal];
    [helpButton addTarget:self action:@selector(helpButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:helpButton];
    [helpButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.pointsTitleLabel.mas_right).offset(5);
        make.centerY.equalTo(self.pointsTitleLabel);
        make.width.height.equalTo(@16);
    }];
    helpButton.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10); // 扩大点击区域

    // 积分数量和状态显示
    UILabel *pointsDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [backView addSubview:pointsDescLabel];
    [pointsDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(pointsTitleLabel);
        make.right.equalTo(@(-15));
        make.height.equalTo(@(22));
    }];
    self.pointsDescLabel = pointsDescLabel;
    
    // 金币支付标题
    UILabel *coinPayTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1];
    coinPayTitleLabel.text = @"金币支付:";
    [backView addSubview:coinPayTitleLabel];
    [coinPayTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(15));
        make.top.equalTo(pointsTitleLabel.mas_bottom).offset(15);
    }];
    self.coinPayTitleLabel = coinPayTitleLabel;
    
    // 需支付金币数
    UILabel *coinPayAmountLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_riseColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [backView addSubview:coinPayAmountLabel];
    [coinPayAmountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(coinPayTitleLabel);
        make.right.equalTo(@(-15));
        make.height.equalTo(@(24));
    }];
    self.coinPayAmountLabel = coinPayAmountLabel;
    
    // 余额显示
    UILabel *balanceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    balanceLB.text = [NSString stringWithFormat:@"金币余额:%ld", userModel.coin];
    [backView addSubview:balanceLB];
    [balanceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(coinPayAmountLabel.mas_right);
        make.top.equalTo(coinPayAmountLabel.mas_bottom).offset(5);
    }];
    self.balanceLB = balanceLB;
    [self.balanceLB mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(coinPayAmountLabel.mas_right);
        make.top.equalTo(coinPayAmountLabel.mas_bottom).offset(5);
    }];
    
    // 积分抵扣提示
    UILabel *pointsTipLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
    pointsTipLabel.text = @"若有积分，优先使用积分抵扣";
    [backView addSubview:pointsTipLabel];
    [pointsTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.balanceLB);
        make.left.equalTo(coinPayTitleLabel);
    }];
}

/// 设置打赏按钮区域
- (void)setupSendGiftView:(UIView *)backView {
    UIButton *sendGiftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [sendGiftBtn setTitle:@"打赏老师" forState:UIControlStateNormal];
    [sendGiftBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    sendGiftBtn.titleLabel.font = FontWithSize(16);
    [sendGiftBtn setBackgroundColor:UIColor.up_riseColor];
    [sendGiftBtn addTarget:self action:@selector(sendGiftBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:sendGiftBtn];
    [sendGiftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo((self.isLandScape ? 300 : 320));
        make.left.equalTo(@(15));
        make.right.equalTo(@(-15));
        make.height.equalTo(@(45));
    }];
    UI_View_Radius(sendGiftBtn, 45 / 2.0);
    self.sendGiftBtn = sendGiftBtn;
    
    UILabel *tipLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary2Color backgroundColor:FMClearColor numberOfLines:1];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"* 打赏功能不能解锁笔记，仅作为对老师的鼓励"];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : UIColor.up_riseColor} range:NSMakeRange(0, 1)];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : UIColor.up_textSecondary2Color} range:NSMakeRange(1, attrStr.length - 1)];
    tipLB.attributedText = attrStr;
    [backView addSubview:tipLB];
    [tipLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(sendGiftBtn.mas_bottom).offset(15);
        make.centerX.equalTo(sendGiftBtn);
        make.height.equalTo(20);
    }];
}

/// 充值成功 更新金币余额
- (void)userCoinUpdate:(NSNotification *)noti {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.balanceLB.text = [NSString stringWithFormat:@"金币余额: %zd", userModel.coin];
    
    // 更新积分数据
    self.userPoints = userModel.points;
    [self updatePaymentDisplay];
}

#pragma mark - 积分相关方法

/// 计算可抵扣的金币数量
- (NSInteger)calculateDeductibleCoins {
    if (!self.selectedGiftModel || [self.selectedGiftModel.price integerValue] <= 0) {
        return 0;
    }

    NSInteger availablePoints = self.userPoints; // 可用积分
    NSInteger maxDeductibleCoins = availablePoints / self.pointsRatio; // 最大抵扣金币
    NSInteger actualOrderAmount = [self.selectedGiftModel.price integerValue]; // 实际订单金额
    return MIN(maxDeductibleCoins, actualOrderAmount);
}

/// 获取当前使用的积分数量（自动抵扣）
- (NSInteger)getCurrentUsePoints {
    return [self calculateDeductibleCoins] * self.pointsRatio;
}

/// 计算实际需要支付的金币数量
- (NSInteger)getActualPayAmount {
    if (!self.selectedGiftModel) {
        return 0;
    }
    NSInteger originalPrice = [self.selectedGiftModel.price integerValue];
    NSInteger deductibleCoins = [self calculateDeductibleCoins];
    return originalPrice - deductibleCoins;
}

/// 更新积分显示
- (void)updatePointsDisplay {
    if (self.userPoints <= 0) {
        // 没有积分
        self.pointsDescLabel.text = @"0 暂无可用";
        self.pointsDescLabel.textColor = UIColor.up_textSecondary2Color;
    } else {
        NSInteger deductibleCoins = [self calculateDeductibleCoins];
        if (deductibleCoins > 0) {
            // 有积分可抵扣，显示格式：当前积分 -抵扣积分
            NSString *pointsText = [NSString stringWithFormat:@"%ld -%ld积分", (long)self.userPoints, (long)deductibleCoins];

            // 创建富文本，"-XX积分"部分使用红色
            NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:pointsText];
            NSRange redRange = [pointsText rangeOfString:[NSString stringWithFormat:@"-%ld积分", (long)deductibleCoins]];
            if (redRange.location != NSNotFound) {
                [attributedText addAttribute:NSForegroundColorAttributeName value:UIColor.up_riseColor range:redRange];
                [attributedText addAttribute:NSForegroundColorAttributeName value:UIColor.up_textPrimaryColor range:NSMakeRange(0, redRange.location)];
            }

            self.pointsDescLabel.attributedText = attributedText;
        } else {
            // 有积分但当前订单无法抵扣
            self.pointsDescLabel.text = [NSString stringWithFormat:@"%ld 暂无可用", (long)self.userPoints];
            self.pointsDescLabel.textColor = UIColor.up_textSecondary2Color;
        }
    }
}

/// 更新支付金额显示
- (void)updatePayAmountDisplay {
    if (!self.selectedGiftModel) {
        self.coinPayAmountLabel.text = @"0金币";
        return;
    }
    
    NSInteger finalPrice = [self getActualPayAmount];
    self.coinPayAmountLabel.text = [NSString stringWithFormat:@"%ld金币", (long)finalPrice];
}

/// 更新支付相关显示
- (void)updatePaymentDisplay {
    [self updatePointsDisplay];
    [self updatePayAmountDisplay];
    [self updateSendButtonState];
}

- (void)helpButtonTapped {
    NSString *str = [NSString stringWithFormat:@"积分仅用于抵扣金币，%zd积分=1金币，可在“我的-福利中心”获得", self.pointsRatio];
    [FMProgressHUD showTextOnlyInView:nil withText:str];
}

/// 更新发送按钮状态
- (void)updateSendButtonState {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSInteger actualPayAmount = [self getActualPayAmount];

    if (actualPayAmount > userModel.coin) {
        [self.sendGiftBtn setTitle:@"金币不足，请先充值" forState:UIControlStateNormal];
    } else {
        [self.sendGiftBtn setTitle:@"打赏老师" forState:UIControlStateNormal];
    }
}

/// 打赏按钮点击事件
- (void)sendGiftBtnClick {
    if (!self.selectedGiftModel) {
        return;
    }
    
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSInteger actualPayAmount = [self getActualPayAmount];

    if (actualPayAmount > userModel.coin) {
        // 跳转充值中心
        if(self.rechargeBlock) {
            self.rechargeBlock();
        }
    } else {
        [self sendGiftWithModel:self.selectedGiftModel];
    }
}

/// 送出礼物
- (void)sendGiftWithModel:(LiveGiftModel *)model {
    UIView *hudBackView = [FMPlayerManager shareManager].player.isFullScreen ? [FMPlayerManager shareManager].player.controlView : self.backView;
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.detailModel.bignameDto.userId certCode:self.detailModel.bignameDto.certCode clickView:self confirmOperation:^{
        NSInteger usePointsValue = [self getCurrentUsePoints];
        [HttpRequestTool liveSendGiftWithRoomId:self.detailModel.liveId giftId:model.giftId usePoints:usePointsValue start:^{
        } failure:^{
            [FMProgressHUD showTextOnlyInView:hudBackView withText:@"请检查您的网络"];
        } success:^(NSDictionary *dic) {
            [FMProgressHUD hiddenProgressHUDViewInView:self.backView];
            if ([dic[@"status"] isEqualToString:@"1"]) {
                if (self.sendGiftBlock) {
                    self.sendGiftBlock(model);
                }
                
                [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:^(NSDictionary *dic) {
                    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                    self.balanceLB.text = [NSString stringWithFormat:@"金币余额: %zd", userModel.coin];
                    // 更新积分数据
                    self.userPoints = userModel.points;
                    [self updatePaymentDisplay];
                }];

                [FMProgressHUD showTextOnlyInView:hudBackView withText:@"赠送成功!"];
            } else {
                if ([dic[@"errcode"] isEqualToString:@"501"]) {
                    [FMProgressHUD showTextOnlyInView:hudBackView withText:@"余额不足，请先充值!"];
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 并跳转充值中心
                        if(self.rechargeBlock) {
                            self.rechargeBlock();
                        }
                    });
                } else {
                    [FMProgressHUD showTextOnlyInView:hudBackView withText:dic[@"errmessage"]];
                }
            }
        }];
    } failurBlock:^{
        if(self.judgeConfirmOrderFailur) {
            self.judgeConfirmOrderFailur();
        }
    }];
}

/// 关闭视图
- (void)closeBtnClick {
    [self removeFromSuperview];
}

/// 充值
- (void)rechargeBtnClick {
    if(self.rechargeBlock) {
        self.rechargeBlock();
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    CGPoint point = [[touches  anyObject] locationInView:self];
    if (self.isLandScape) {
        if (point.x > (UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT)) {
            return;
        }
    } else {
        if (point.y > (UI_SCREEN_HEIGHT - self.backViewHeight)) {
            return;
        }
    }
    self.hidden = YES;
}

#pragma mark - public
- (void)show {
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    if (!self.superview) {
        [[FMHelper getCurrentVC].view addSubview:self];
    } else {
        self.hidden = NO;
    }
}

#pragma mark - collectionViewDelegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    GiftItemCell *cell = [collectionView reuseCellClass:[GiftItemCell class] indexPath:indexPath];
    cell.model = self.dataArr[indexPath.item];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    for (NSInteger i = 0; i < self.dataArr.count; i ++) {
        LiveGiftModel *model = self.dataArr[i];
        model.isSelected = indexPath.item == i;
        if (model.isSelected) {
            self.selectedGiftModel = model;
        }
    }
    [self.collectionView reloadData];
    
    // 更新支付相关显示
    [self updatePaymentDisplay];
}

#pragma mark - lazy
- (NSMutableArray *)itemViewArr {
    if (!_itemViewArr) {
        _itemViewArr = [NSMutableArray array];
    }
    return _itemViewArr;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        // 修改为横向滚动，固定宽高为100x130
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero scrollDirection:UICollectionViewScrollDirectionHorizontal minimumInteritemSpacing:10 minimumLineSpacing:10 itemSize:CGSizeMake(100, 130) sectionInset:UIEdgeInsetsZero flowLayoutDeleaget:self dataSource:self viewController:nil];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
        [_collectionView registerCellClass:[GiftItemCell class]];
    }
    return _collectionView;
}


- (void)setDataArr:(NSArray *)dataArr {
    NSArray *arr = [NSArray modelArrayWithClass:[LiveGiftModel class] json:dataArr];
    _dataArr = arr;
    if (self.giftId > 0) {
        for (LiveGiftModel *model in arr) {
            if (model.giftId == self.giftId) {
                model.isSelected = YES;
                self.selectedGiftModel = model;
            }
        }
        self.giftId = 0;
    } else {
        // 默认选中第一个
        LiveGiftModel *model = arr.firstObject;
        model.isSelected = YES;
        self.selectedGiftModel = model;
    }
    [self.collectionView reloadData];
    
    // 更新支付相关显示
    [self updatePaymentDisplay];
}

@end
