//
//  FMNetworkStatusMonitor.m
//  QCYZT
//
//  Created by zeng on 2025/3/24.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMNetworkStatusMonitor.h"
#import <Network/Network.h>
#import <SystemConfiguration/SystemConfiguration.h>
#import <netinet/in.h>
#import <arpa/inet.h>

NSNotificationName const NetworkStatusDidChangeNotification = @"NetworkStatusDidChangeNotification";

@interface FMNetworkStatusMonitor ()

@property (nonatomic, assign, readwrite) NetworkStatus currentStatus;
@property (nonatomic, assign, readwrite) BOOL isConnected;
@property (nonatomic, strong) dispatch_queue_t monitorQueue;
@property (nonatomic, strong) nw_path_monitor_t monitor;

@property (nonatomic, assign, readwrite) NetworkAuthorizationStatus authorizationStatus;
@property (nonatomic, strong) UIAlertController *authAlertController;
@property (nonatomic, assign) SCNetworkReachabilityRef reachability;

@end

@implementation FMNetworkStatusMonitor

+ (instancetype)sharedMonitor {
    static FMNetworkStatusMonitor *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _monitor = NULL;
        _monitorQueue = dispatch_queue_create("com.fm.networkMonitor", DISPATCH_QUEUE_SERIAL);
        _currentStatus = NetworkStatusUnavailable;
        _isConnected = NO;
        _connectionInfo = @{};
        _authorizationStatus = NetworkAuthorizationStatusUnknown;
        _shouldShowAuthorizationAlert = YES;
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                               selector:@selector(applicationDidBecomeActive)
                                                   name:UIApplicationDidBecomeActiveNotification
                                                 object:nil];
    }
    return self;
}

#warning 网络状态变化判断有误
- (void)startMonitoring {
    [self stopMonitoring];
    
    // 创建网络监控器
    self.monitor = nw_path_monitor_create();
    
    if (self.monitor == NULL) {
        NSLog(@"创建网络监控器失败");
        return;
    }
        
    // 设置监控回调
    __weak typeof(self) weakSelf = self;
    nw_path_monitor_set_update_handler(self.monitor, ^(nw_path_t _Nonnull path) {
        // 判断是否有网络连接
        BOOL isConnected = (nw_path_get_status(path) == nw_path_status_satisfied);
        
        // 确定网络类型
        NetworkStatus newStatus = NetworkStatusUnavailable;
        if (isConnected) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                newStatus = NetworkStatusWiFi;
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                newStatus = NetworkStatusCellular;
            } else if (nw_path_uses_interface_type(path, nw_interface_type_wired)) {
                newStatus = NetworkStatusWired;
            } else {
                newStatus = NetworkStatusOther;
            }
        }
                
        // 在主线程更新状态并发送通知
        dispatch_async(dispatch_get_main_queue(), ^{
            __strong typeof(weakSelf) strongSelf = weakSelf;
            if (strongSelf) {
                
                // 添加 block 回调
                if (strongSelf.statusChangeBlock) {
                    strongSelf.statusChangeBlock(newStatus);
                }
                
                // 发送网络状态变化通知
                [[NSNotificationCenter defaultCenter] postNotificationName:kNetworkStatusChangedNotification
                                                                    object:strongSelf
                                                                  userInfo:nil];
                
                if (!strongSelf.isConnected && isConnected) {
                    [[NSNotificationCenter defaultCenter] postNotificationName:kNetworkStatusChangedToOnlineNotification
                                                                        object:strongSelf
                                                                      userInfo:nil];
                    
                    NSLog(@"无网络变成有网络");
                }
                
                strongSelf.isConnected = isConnected;
                strongSelf.currentStatus = newStatus;

                
                if (isConnected) {
                    NSString *statusString = @"未知网络";
                    switch (newStatus) {
                        case NetworkStatusWiFi:
                            statusString = @"WiFi网络";
                            break;
                        case NetworkStatusCellular:
                            statusString = @"蜂窝网络";
                            break;
                        case NetworkStatusWired:
                            statusString = @"有线网络";
                            break;
                        case NetworkStatusOther:
                            statusString = @"其他网络";
                            break;
                        default:
                            break;
                    }
                    FMLog(@"已连接: %@", statusString);
                } else {
                    FMLog(@"网络已断开， -- %zd", newStatus);
                }
            }
        });
    });
    
    // 设置监听队列
    nw_path_monitor_set_queue(self.monitor, self.monitorQueue);
    
    // 启动监听
    nw_path_monitor_start(self.monitor);
}

- (void)stopMonitoring {
    if (self.monitor) {
        nw_path_monitor_cancel(self.monitor);
        self.monitor = nil;
    }
}

- (void)applicationDidBecomeActive {
    [self checkNetworkAuthorization];
}

- (void)checkNetworkAuthorization {
    __weak typeof(self) weakSelf = self;
    dispatch_async(self.monitorQueue, ^{
        // 创建一个零地址用于检查网络
        struct sockaddr_in zeroAddress;
        bzero(&zeroAddress, sizeof(zeroAddress));
        zeroAddress.sin_len = sizeof(zeroAddress);
        zeroAddress.sin_family = AF_INET;
        
        // 创建 reachability 对象
        SCNetworkReachabilityRef reachability = SCNetworkReachabilityCreateWithAddress(NULL, (const struct sockaddr *)&zeroAddress);
        self.reachability = reachability;
        
        if (reachability != NULL) {
            SCNetworkReachabilityFlags flags;
            BOOL success = SCNetworkReachabilityGetFlags(reachability, &flags);
            
            dispatch_async(dispatch_get_main_queue(), ^{
                __strong typeof(weakSelf) strongSelf = weakSelf;
                if (!strongSelf) return;
                
                NetworkAuthorizationStatus oldStatus = strongSelf.authorizationStatus;
                
                if (!success) {
                    strongSelf.authorizationStatus = NetworkAuthorizationStatusUnknown;
                } else if ((flags & kSCNetworkReachabilityFlagsReachable) &&
                          !(flags & kSCNetworkReachabilityFlagsConnectionRequired)) {
                    strongSelf.authorizationStatus = NetworkAuthorizationStatusAuthorized;
                } else {
                    strongSelf.authorizationStatus = NetworkAuthorizationStatusDenied;
                    if (strongSelf.shouldShowAuthorizationAlert) {
//                        [strongSelf showNetworkAuthorizationAlert];
                    }
                }
                
                // 如果状态发生变化，调用 block
                if (oldStatus != strongSelf.authorizationStatus && strongSelf.authorizationChangeBlock) {
                    strongSelf.authorizationChangeBlock(strongSelf.authorizationStatus);
                }
                
                // 释放 reachability
                if (strongSelf.reachability) {
                    CFRelease(strongSelf.reachability);
                    strongSelf.reachability = NULL;
                }
            });
        }
    });
}

- (void)showNetworkAuthorizationAlert {
    // 确保在主线程执行
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self showNetworkAuthorizationAlert];
        });
        return;
    }
    
    // 避免重复显示
    if (self.authAlertController.presentingViewController || 
        [self.authAlertController isBeingPresented]) {
        return;
    }
    
    // 获取当前最顶层的视图控制器
    UIViewController *topViewController = nil;
    UIWindow *keyWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        NSSet *scenes = UIApplication.sharedApplication.connectedScenes;
        UIWindowScene *windowScene = [scenes anyObject];
        NSArray *windows = windowScene.windows;
        keyWindow = windows.firstObject;
        
        for (UIWindow *window in windows) {
            if (window.isKeyWindow) {
                keyWindow = window;
                break;
            }
        }
    } else {
        keyWindow = UIApplication.sharedApplication.keyWindow;
    }
    
    // 获取最顶层的控制器
    topViewController = keyWindow.rootViewController;
    while (topViewController.presentedViewController) {
        topViewController = topViewController.presentedViewController;
    }
    
    // 如果是 UINavigationController，获取最顶层的控制器
    if ([topViewController isKindOfClass:[UINavigationController class]]) {
        topViewController = [(UINavigationController *)topViewController visibleViewController];
    }
    
    // 确保有可用的视图控制器
    if (topViewController) {
        [topViewController presentViewController:self.authAlertController
                                     animated:YES 
                                   completion:^{
        }];
    } else {
        NSLog(@"未找到合适的视图控制器来显示网络授权提示框");
    }
}

- (UIAlertController *)authAlertController {
    if (!_authAlertController) {
        _authAlertController = [UIAlertController alertControllerWithTitle:@"网络访问受限"
                                                                  message:@"请在设置中允许应用访问网络"
                                                           preferredStyle:UIAlertControllerStyleAlert];
        
        UIAlertAction *settingsAction = [UIAlertAction actionWithTitle:@"设置"
                                                               style:UIAlertActionStyleDefault
                                                             handler:^(UIAlertAction * _Nonnull action) {
            NSURL *settingsURL = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:settingsURL]) {
                if (@available(iOS 10.0, *)) {
                    [[UIApplication sharedApplication] openURL:settingsURL
                                                    options:@{}
                                          completionHandler:nil];
                } else {
                    [[UIApplication sharedApplication] openURL:settingsURL];
                }
            }
        }];
        
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消"
                                                             style:UIAlertActionStyleCancel
                                                           handler:nil];
        
        [_authAlertController addAction:settingsAction];
        [_authAlertController addAction:cancelAction];
    }
    return _authAlertController;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self stopMonitoring];
    
    if (_reachability) {
        CFRelease(_reachability);
        _reachability = NULL;
    }
}

@end

